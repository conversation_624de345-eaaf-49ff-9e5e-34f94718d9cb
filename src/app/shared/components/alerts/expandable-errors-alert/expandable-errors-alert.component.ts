import { Component, Input } from '@angular/core';
import { fadeRowAnimation } from 'app/core/animations/fade.animation';

export type ErrorDetails = {
    id: string;
    title: string | null;
    description: string;
}

@Component({
    selector: 'avl-expandable-errors-alert',
    templateUrl: './expandable-errors-alert.component.html',
    styleUrls: ['./expandable-errors-alert.component.scss'],
    animations: [fadeRowAnimation],
})
export class ExpandableErrorsAlertComponent {
    @Input()
    public errors: ErrorDetails[];

    @Input()
    public isBright = false;

    public trackByErrors(index: number, error: ErrorDetails): string {
        return error.id;
    }
}
