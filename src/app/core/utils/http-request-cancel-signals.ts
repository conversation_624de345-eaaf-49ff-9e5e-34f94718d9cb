import { Subject } from 'rxjs';

export type HttpRequestCancelSignal = {
    id: string;
    instance: Subject<void>;
}

export abstract class HttpRequestCancelSignals {
    private readonly signals: HttpRequestCancelSignal[] = [];

    public cancelAll(): void {
        for (let i = 0; i < this.signals.length; i++) {
            const signal = this.signals.pop();
            signal.instance.next();
            signal.instance.complete();
        }
    }

    public cancel(id: string): void {
        const signal = this.signals.find((signal) => signal.id === id);
        if (signal) {
            signal.instance.next();
            signal.instance.complete();
            this.signals.filter((signal) => signal.id !== id);
        }
    }

    protected createCancelSignal(requestId: string): Subject<void> {
        const destroySignal = new Subject<void>();

        this.cancel(requestId);
        this.addCancelSignal(requestId, destroySignal);

        return destroySignal;
    }

    protected addCancelSignal(requestId: string, destroySignal: Subject<void>): void {
        this.signals.push({
            id: requestId,
            instance: destroySignal,
        });
    }
}
