// FIXME: this type is not based on the type from backend
import { InfoMessage } from './info-message.type.js';

export type SearchFile = {
  id: string;
  address: string;
  fileName: string;
  isAccepted: boolean;
  isProcessed: boolean;
  isError: boolean;
  linkedTitles: string[];
  messages: InfoMessage[];
  metadata: {
    dateUploaded?: string;
    fileName?: string;
    pages?: number;
    size?: string;
  };
  type: string;
  uploadedAt: string;
  bindTo?: string;
}

export type SearchFileType = {
  [type: string]: string;
}

export function createEmptySearchFile(id: string): SearchFile {
    return {
        id,
        address: '',
        fileName: '',
        isAccepted: false,
        isProcessed: false,
        isError: false,
        linkedTitles: [],
        messages: [],
        metadata: {},
        type: '',
        uploadedAt: '',
    };
}
