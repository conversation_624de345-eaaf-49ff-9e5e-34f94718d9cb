import { Injectable } from '@angular/core';
import { Order, QueryEntity } from '@datorama/akita';
import { SearchFileSortBy, SearchFilesState, SearchFilesStore } from './search-files.store';
import { Observable } from 'rxjs';
import { SearchFile } from '../../types/search-file.type';
import { map } from 'rxjs/operators';

@Injectable()
export class SearchFilesQuery extends QueryEntity<SearchFilesState> {
    public readonly fileWaitsUploadingCount$ = this.selectCount(({ isProcessed }) => !isProcessed);

    constructor(protected store: SearchFilesStore) {
        super(store);
    }

    public isTemporaryFile(fileId: string): boolean {
        return this.hasEntity(({ id, uploadedAt }) => id === fileId && !uploadedAt);
    }

    public isAllFileAccepted(): boolean {
        const unacceptedFileExists = this.hasEntity(({ isAccepted }) => !isAccepted);

        return !unacceptedFileExists;
    }

    public selectSortedFiles$(): Observable<SearchFile[]> {
        return this.selectAll()
            .pipe(
                map((files) => files.filter((file) => !file.isError)),
                map((searches) => searches.sort((a, b) => this.sortState(a, b, this.getValue().sortBy, this.getValue().sortByOrder))),
                map((searches) => searches.sort((file) => this.sortByAccepted(file))),
            );
    }

    public selectFailedFiles$(): Observable<SearchFile[]> {
        return this.selectAll()
            .pipe(
                map((files) => files.filter((file) => file.isError)),
            );
    }

    public sortState(a: SearchFile, b: SearchFile, sortBy: SearchFileSortBy, sortByOrder: Order = Order.ASC): number {
        let sortValue = 0;
        const isReverseOrder = sortByOrder === Order.DESC;

        switch (sortBy) {
            case 'pages':
                sortValue = this.sortByPages(a, b);
                break;
            case 'size':
                sortValue = this.sortBySize(a, b);
                break;
            case 'linkedTitles':
                sortValue = this.sortByLinkedTitles(a, b);
                break;
            case 'type':
                sortValue = a.type.localeCompare(b.type);
                break;
            case 'fileName':
            default:
                sortValue = a.fileName.localeCompare(b.fileName);
        }

        return isReverseOrder ? -1 * sortValue : sortValue;
    }

    public sortBySize(a: SearchFile, b: SearchFile): number {
        const sizeA = parseFloat(a.metadata.size);
        const sizeB = parseFloat(b.metadata.size);
        const unitA = a.metadata.size.slice(-2);
        const unitB = b.metadata.size.slice(-2);
        const unitComparison = unitA.localeCompare(unitB);

        return unitComparison || sizeA - sizeB;
    }

    public sortByPages(a: SearchFile, b: SearchFile): number {
        const pageA = Number(a.metadata.pages);
        const pageB = Number(b.metadata.pages);

        return pageA - pageB;
    }

    public sortByLinkedTitles(a: SearchFile, b: SearchFile): number {
        const lengthA = a.linkedTitles.length;
        const lengthB = b.linkedTitles.length;

        return lengthA - lengthB;
    }

    public sortByAccepted(searchFile: SearchFile): number {
        return searchFile.isAccepted ? -1 : 1;
    }
}
